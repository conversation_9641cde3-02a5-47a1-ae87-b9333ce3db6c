{"timestamp": "2025-07-18T01:37:20.642891", "duration": 53.0747857093811, "summary": {"total_tests": 17, "passed": 6, "failed": 11}, "results": [{"name": "Database Connectivity", "category": "Backend", "status": "PASSED", "message": "All 6 databases connected successfully", "details": {"atlas.db": "Connected", "atlas_memory.db": "Connected", "atlas_rag.db": "Connected", "atlas_compliance.db": "Connected", "atlas_feedback.db": "Connected", "atlas_enhanced_memory.db": "Connected"}, "errors": [], "warnings": [], "performance": {"duration": 0.0001621246337890625}}, {"name": "Configuration Loading", "category": "Backend", "status": "PASSED", "message": "Configuration loaded successfully", "details": {"API Keys": {"OpenAI": true, "Alpaca": true, "FMP": true, "Predicto": true}, "Environment": "development", "Port": 8001}, "errors": [], "warnings": [], "performance": {"duration": 7.152557373046875e-06}}, {"name": "Core Engine Initialization", "category": "Backend", "status": "PASSED", "message": "All engines initialized successfully", "details": {"Engines": {"database": "active", "utils": "active", "market": "active", "risk": "active", "trading": "active", "education": "active", "ai": "active", "lee_method": "initialized"}, "Active Engines": "8/8"}, "errors": [], "warnings": [], "performance": {"duration": 8.124705076217651}}, {"name": "AI Core Functionality", "category": "Backend", "status": "PASSED", "message": "AI core operational", "details": {"AI Response": "Generated successfully", "Response Length": 801, "Response Type": "education", "Confidence": 0.9}, "errors": [], "warnings": [], "performance": {"duration": 0.00014591217041015625}}, {"name": "Market Data Access", "category": "Backend", "status": "PASSED", "message": "Market data access working", "details": {"AAPL Price": "$210.24", "Symbol": "AAPL", "Change": "0.00", "Data Source": "FMP API"}, "errors": [], "warnings": [], "performance": {"duration": 0.2672572135925293}}, {"name": "<PERSON>", "category": "Backend", "status": "PASSED", "message": "Scanner operational (no patterns in test data)", "details": {"Pattern Detection": "Functional", "Scanner Type": "<PERSON> (5-point TTM Squeeze)"}, "errors": [], "warnings": [], "performance": {"duration": 0.005162954330444336}}, {"name": "Health Check API", "category": "Integration", "status": "FAILED", "message": "Server not running on port 8001", "details": {}, "errors": ["Please start the server: python atlas_server.py"], "warnings": [], "performance": {"duration": 4.040429592132568}}, {"name": "Chat API", "category": "Integration", "status": "FAILED", "message": "Chat API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3E1950>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3E1950>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.03099250793457}}, {"name": "Market Quote API", "category": "Integration", "status": "FAILED", "message": "Market API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/quote/AAPL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3E1090>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/quote/AAPL (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3E1090>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.066930294036865}}, {"name": "Scanner Status API", "category": "Integration", "status": "FAILED", "message": "Scanner API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3FB820>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3FB820>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.0771565437316895}}, {"name": "Portfolio API", "category": "Integration", "status": "FAILED", "message": "Portfolio API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/portfolio (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3FAD70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/portfolio (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3FAD70>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.047820091247559}}, {"name": "Risk Assessment API", "category": "Integration", "status": "FAILED", "message": "Risk API error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/risk-assessment (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE4095B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/risk-assessment (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE4095B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.0361247062683105}}, {"name": "Web Interface Loading", "category": "Frontend", "status": "FAILED", "message": "Web interface error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE438270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE438270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.046477317810059}}, {"name": "WebSocket Scanner Connection", "category": "Frontend", "status": "FAILED", "message": "Server not accessible - cannot test WebSocket", "details": {}, "errors": [], "warnings": [], "performance": {"duration": 4.076596021652222}}, {"name": "Trading Analysis Workflow", "category": "End-to-End", "status": "FAILED", "message": "Workflow error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE412250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE412250>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.071883916854858}}, {"name": "Scanner to Signal Display", "category": "End-to-End", "status": "FAILED", "message": "Scanner workflow error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE412950>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/scanner/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE412950>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.07468318939209}}, {"name": "Educational Query Workflow", "category": "End-to-End", "status": "FAILED", "message": "Educational workflow error: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3F7890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))", "details": {}, "errors": ["HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/v1/chat (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001DDEE3F7890>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))"], "warnings": [], "performance": {"duration": 4.087993860244751}}]}